import React from "react";
import { Pressable, View, Text } from "react-native";
import { Match } from "../../../types/matches";
import { ChevronRightIcon, Icon } from "@/components/ui/icon";
import {
  Edit2Icon,
  Trash2Icon,
  BanIcon,
  UserRoundPenIcon,
  UserXIcon,
} from "lucide-react-native";
import SlideButton from "@/components/k-components/SlideButton";
import { Divider } from "@/components/ui/divider";
import { triggerHapticFeedback } from "@/utils";
import { useRouter } from "expo-router";
import SCREENS from "@/constants/Screens";

interface MatchActionsProps {
  match: Match;
  onClose: () => void;
}

interface ActionItem {
  icon: any;
  label: string;
  clickHandler: () => void;
  textColor?: string;
}

const SettingIcon = ({
  onPress,
  icon,
  color,
}: {
  onPress: () => void;
  icon: any;
  color: string;
}) => {
  const handlePress = () => {
    triggerHapticFeedback();
    onPress();
  };
  return (
    <Pressable
      onPress={handlePress}
      className={`p-2 border border-${color} rounded-full`}
    >
      <Icon as={icon} size="sm" className={`text-${color}`} />
    </Pressable>
  );
};

const MatchActions: React.FC<MatchActionsProps> = ({ match, onClose }) => {
  const router = useRouter();
  const handleStartMatch = () => {
    // TODO: Implement start match logic
    console.log("Starting match:", match.id);
  };

  const handleEditMatch = () => {
    onClose();
    router.push({
      pathname: SCREENS.CREATE_TOURNAMENT_MATCH,
      params: {
        "tournament-id": match.tournament_id,
        mode: "edit",
        "match-id": match.id,
      },
    });
  };

  const handleDeleteMatch = () => {
    // TODO: Implement delete match logic
    console.log("Deleting match:", match.id);
  };

  const handleCancelMatch = () => {
    // TODO: Implement cancel match logic
    console.log("Cancelling match:", match.id);
  };

  const handleUpdateScore = () => {
    // TODO: Implement update score logic
    console.log("Updating score for match:", match.id);
  };

  const handleMarkAsWalkover = () => {
    // TODO: Implement mark as walkover logic
    console.log("Marking match as walkover:", match.id);
  };

  const headerActions = [
    {
      icon: Edit2Icon,
      color: "gray-400",
      clickHandler: handleEditMatch,
    },
    {
      icon: Trash2Icon,
      color: "red-500",
      clickHandler: handleDeleteMatch,
    },
  ];

  const menuActions: ActionItem[] = [
    {
      icon: BanIcon,
      label: "Cancel match",
      clickHandler: handleCancelMatch,
      textColor: "text-typography-700",
    },
    {
      icon: UserRoundPenIcon,
      label: "Update Final score",
      clickHandler: handleUpdateScore,
      textColor: "text-typography-700",
    },
    {
      icon: UserXIcon,
      label: "Mark as Walkover",
      clickHandler: handleMarkAsWalkover,
      textColor: "text-typography-700",
    },
  ];

  return (
    <View className="flex">
      <View className="rounded-xl bg-white border border-gray-200 shadow-md pt-3">
        <View className="flex-row justify-between items-center mb-3 px-4">
          <Text className="text-sm font-urbanistExtraBold tracking-widest text-gray-500 self-center">
            MATCH ACTIONS
          </Text>
          <View className="flex-row justify-end gap-3">
            {headerActions.map((action, index) => (
              <SettingIcon
                key={index}
                onPress={action.clickHandler}
                icon={action.icon}
                color={action.color}
              />
            ))}
          </View>
        </View>
        <View className="border-t border-gray-300 border-dashed" />
        <View className="px-4">
          {menuActions.map((action, index) => (
            <View key={index}>
              <Pressable
                className="flex-row justify-between items-center py-4"
                onPress={action.clickHandler}
              >
                <View className="flex-row items-center space-x-2 gap-3">
                  <Icon
                    as={action.icon}
                    size="sm"
                    className={action.textColor || "text-typography-700"}
                  />
                  <Text
                    className={`text-sm font-urbanistSemiBold ${
                      action.textColor || "text-typography-700"
                    }`}
                  >
                    {action.label}
                  </Text>
                </View>
                <Icon
                  as={ChevronRightIcon}
                  size="sm"
                  className="text-typography-700"
                />
              </Pressable>
              {index < menuActions.length - 1 && (
                <Divider className="w-full self-center" />
              )}
            </View>
          ))}
        </View>
      </View>

      {/* Slide Button for Start Match */}
      <View className="mt-4">
        <SlideButton
          onSlideComplete={handleStartMatch}
          text="Slide to Start Match"
          backgroundColor="bg-primary-0"
          textColor="text-white"
          width="auto"
          height={52}
        />
      </View>
    </View>
  );
};

export default MatchActions;
