// services/matches.ts
import type { PostgrestError } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { Match, CreateMatchInput, SCHEDULED } from '@/types/matches';
import { getCurrentUser } from '@/utils/auth-utils';

export type FetchMatchesResult = {
  matches: Match[];
  error: string | null;
};

export async function fetchMatchesForTournament(
  tournamentId: string
): Promise<FetchMatchesResult> {
  const {
    data,
    error,
  }: { data: Match[] | null; error: PostgrestError | null } = await supabase
    .from('matches')
    .select('*')
    .eq('tournament_id', tournamentId)
    .order('match_number', { ascending: true });

  if (error) {
    console.error('[fetchMatchesForTournament]', error);
    return {
      matches: [],
      error: error.message,
    };
  }

  return {
    matches: data ?? [],
    error: null,
  };
}

export async function fetchMatchById(matchId: string): Promise<{
  success: boolean;
  match?: Match;
  error?: string;
}> {
  try {
    const { data: match, error } = await supabase
      .from('matches')
      .select('*')
      .eq('id', matchId)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, match };
  } catch (err: any) {
    return { success: false, error: err.message || 'Unknown error occurred' };
  }
}

export async function updateMatch(
  matchId: string,
  matchData: Partial<CreateMatchInput>
): Promise<{ success: boolean; match?: Match; error?: string }> {
  try {
    const { success, user, error: userError } = await getCurrentUser();

    if (!success || !user) {
      return {
        success: false,
        error: userError || 'User not authenticated',
      };
    }

    // Prepare the match data for update (exclude fields that shouldn't be updated)
    const updateData = {
      round_name: matchData.round_name || null,
      stage: matchData.stage || null,
      participant_1_id: matchData.participant_1_id || null,
      participant_1_name: matchData.participant_1_name || null,
      participant_2_id: matchData.participant_2_id || null,
      participant_2_name: matchData.participant_2_name || null,
      scheduled_date: matchData.scheduled_date || null,
      match_venue: matchData.match_venue || null,
      court_field_number: matchData.court_field_number || null,
      status: matchData.status || null,
    };

    const { data: updatedMatch, error } = await supabase
      .from('matches')
      .update(updateData)
      .eq('id', matchId)
      .select()
      .single();

    if (error) {
      console.error('[updateMatch] Error updating match:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      match: updatedMatch,
    };
  } catch (error: any) {
    console.error('[updateMatch] Unexpected error:', error);
    return {
      success: false,
      error: error.message || 'Failed to update match',
    };
  }
}

export async function createMatch(
  matchData: CreateMatchInput
): Promise<{ success: boolean; match?: Match; error?: string }> {
  try {
    const { success, user, error: userError } = await getCurrentUser();

    if (!success || !user) {
      return {
        success: false,
        error: userError || 'User not authenticated',
      };
    }

    // Get the next match number for this tournament
    const { data: existingMatches, error: countError } = await supabase
      .from('matches')
      .select('match_number')
      .eq('tournament_id', matchData.tournament_id)
      .order('match_number', { ascending: false })
      .limit(1);

    if (countError) {
      console.error('[createMatch] Error getting match count:', countError);
      return {
        success: false,
        error: countError.message,
      };
    }

    const nextMatchNumber =
      existingMatches && existingMatches.length > 0
        ? (existingMatches[0].match_number || 0) + 1
        : 1;

    // Prepare the match data for insertion
    const insertData = {
      tournament_id: matchData.tournament_id,
      match_number: matchData.match_number || nextMatchNumber,
      round_name: matchData.round_name || null,
      stage: matchData.stage || null,
      participant_type: matchData.participant_type,
      participant_1_id: matchData.participant_1_id || null,
      participant_1_name: matchData.participant_1_name || null,
      participant_2_id: matchData.participant_2_id || null,
      participant_2_name: matchData.participant_2_name || null,
      scheduled_date: matchData.scheduled_date || null,
      match_venue: matchData.match_venue || null,
      court_field_number: matchData.court_field_number || null,
      status: matchData.status || SCHEDULED,
      created_by: user.id,
    };

    const { data: createdMatch, error } = await supabase
      .from('matches')
      .insert([insertData])
      .select()
      .single();

    if (error) {
      console.error('[createMatch] Error creating match:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      match: createdMatch,
    };
  } catch (error: any) {
    console.error('[createMatch] Unexpected error:', error);
    return {
      success: false,
      error: error.message || 'Failed to create match',
    };
  }
}
